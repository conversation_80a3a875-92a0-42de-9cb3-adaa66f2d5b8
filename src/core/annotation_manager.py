#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import json
import copy


@dataclass
class AnnotationAction:
    """表示一个注释操作，用于撤销/重做"""
    action_type: str  # 'add', 'edit', 'delete', 'merge', 'split'
    timestamp: datetime
    key: str
    old_data: Any = None
    new_data: Any = None
    frame_range: Tuple[int, int] = None
    description: str = ""


@dataclass
class Annotation:
    """表示一个注释"""
    key: str
    start_frame: int
    end_frame: int
    description: str
    created_at: datetime = field(default_factory=datetime.now)
    modified_at: datetime = field(default_factory=datetime.now)
    confidence: float = 1.0
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if self.start_frame > self.end_frame:
            self.start_frame, self.end_frame = self.end_frame, self.start_frame
    
    def get_duration(self) -> int:
        """获取持续时间"""
        return self.end_frame - self.start_frame + 1
    
    def contains_frame(self, frame: int) -> bool:
        """检查是否包含指定帧"""
        return self.start_frame <= frame <= self.end_frame
    
    def overlaps_with(self, other: 'Annotation') -> bool:
        """检查是否与另一个注释重叠"""
        return not (self.end_frame < other.start_frame or self.start_frame > other.end_frame)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'key': self.key,
            'start_frame': self.start_frame,
            'end_frame': self.end_frame,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'modified_at': self.modified_at.isoformat(),
            'confidence': self.confidence,
            'tags': self.tags,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Annotation':
        """从字典创建注释"""
        return cls(
            key=data['key'],
            start_frame=data['start_frame'],
            end_frame=data['end_frame'],
            description=data['description'],
            created_at=datetime.fromisoformat(data.get('created_at', datetime.now().isoformat())),
            modified_at=datetime.fromisoformat(data.get('modified_at', datetime.now().isoformat())),
            confidence=data.get('confidence', 1.0),
            tags=data.get('tags', []),
            metadata=data.get('metadata', {})
        )


class AnnotationManager:
    """注释管理器，提供撤销/重做、批量编辑、验证等功能"""
    
    def __init__(self, max_history: int = 100):
        self.annotations: Dict[str, List[Annotation]] = {}  # key -> annotations
        self.action_history: List[AnnotationAction] = []
        self.history_index = -1
        self.max_history = max_history
        
        # 验证规则
        self.validation_rules = []
        
        # 统计信息
        self.stats = {
            'total_annotations': 0,
            'total_duration': 0,
            'keys_count': 0,
            'avg_duration': 0
        }
    
    def add_annotation(self, annotation: Annotation) -> bool:
        """添加注释"""
        try:
            # 验证注释
            if not self.validate_annotation(annotation):
                return False
            
            # 记录操作
            action = AnnotationAction(
                action_type='add',
                timestamp=datetime.now(),
                key=annotation.key,
                new_data=annotation.to_dict(),
                frame_range=(annotation.start_frame, annotation.end_frame),
                description=f"添加注释: {annotation.description[:50]}"
            )
            
            # 添加到对应键的列表
            if annotation.key not in self.annotations:
                self.annotations[annotation.key] = []
            
            self.annotations[annotation.key].append(annotation)
            
            # 按起始帧排序
            self.annotations[annotation.key].sort(key=lambda a: a.start_frame)
            
            # 记录操作历史
            self._add_to_history(action)
            
            # 更新统计
            self._update_stats()
            
            return True
            
        except Exception as e:
            print(f"添加注释失败: {e}")
            return False
    
    def edit_annotation(self, key: str, start_frame: int, new_annotation: Annotation) -> bool:
        """编辑注释"""
        try:
            # 查找要编辑的注释
            old_annotation = self.find_annotation(key, start_frame)
            if not old_annotation:
                return False
            
            # 验证新注释
            if not self.validate_annotation(new_annotation):
                return False
            
            # 记录操作
            action = AnnotationAction(
                action_type='edit',
                timestamp=datetime.now(),
                key=key,
                old_data=old_annotation.to_dict(),
                new_data=new_annotation.to_dict(),
                frame_range=(old_annotation.start_frame, old_annotation.end_frame),
                description=f"编辑注释: {old_annotation.description[:30]} -> {new_annotation.description[:30]}"
            )
            
            # 更新注释
            annotations = self.annotations[key]
            for i, ann in enumerate(annotations):
                if ann.start_frame == start_frame:
                    annotations[i] = new_annotation
                    new_annotation.modified_at = datetime.now()
                    break
            
            # 重新排序
            annotations.sort(key=lambda a: a.start_frame)
            
            # 记录操作历史
            self._add_to_history(action)
            
            # 更新统计
            self._update_stats()
            
            return True
            
        except Exception as e:
            print(f"编辑注释失败: {e}")
            return False
    
    def delete_annotation(self, key: str, start_frame: int) -> bool:
        """删除注释"""
        try:
            # 查找要删除的注释
            annotation = self.find_annotation(key, start_frame)
            if not annotation:
                return False
            
            # 记录操作
            action = AnnotationAction(
                action_type='delete',
                timestamp=datetime.now(),
                key=key,
                old_data=annotation.to_dict(),
                frame_range=(annotation.start_frame, annotation.end_frame),
                description=f"删除注释: {annotation.description[:50]}"
            )
            
            # 删除注释
            self.annotations[key] = [a for a in self.annotations[key] if a.start_frame != start_frame]
            
            # 记录操作历史
            self._add_to_history(action)
            
            # 更新统计
            self._update_stats()
            
            return True
            
        except Exception as e:
            print(f"删除注释失败: {e}")
            return False
    
    def merge_annotations(self, key: str, start_frames: List[int], new_description: str = "") -> bool:
        """合并多个注释"""
        try:
            if len(start_frames) < 2:
                return False
            
            # 获取要合并的注释
            annotations_to_merge = []
            for start_frame in start_frames:
                ann = self.find_annotation(key, start_frame)
                if ann:
                    annotations_to_merge.append(ann)
            
            if len(annotations_to_merge) < 2:
                return False
            
            # 计算合并后的范围
            min_start = min(ann.start_frame for ann in annotations_to_merge)
            max_end = max(ann.end_frame for ann in annotations_to_merge)
            
            # 合并描述
            if not new_description:
                descriptions = [ann.description for ann in annotations_to_merge if ann.description]
                new_description = "; ".join(descriptions)
            
            # 创建新注释
            merged_annotation = Annotation(
                key=key,
                start_frame=min_start,
                end_frame=max_end,
                description=new_description,
                confidence=min(ann.confidence for ann in annotations_to_merge),
                tags=list(set(tag for ann in annotations_to_merge for tag in ann.tags))
            )
            
            # 记录操作
            action = AnnotationAction(
                action_type='merge',
                timestamp=datetime.now(),
                key=key,
                old_data=[ann.to_dict() for ann in annotations_to_merge],
                new_data=merged_annotation.to_dict(),
                frame_range=(min_start, max_end),
                description=f"合并 {len(annotations_to_merge)} 个注释"
            )
            
            # 删除原注释并添加新注释
            self.annotations[key] = [a for a in self.annotations[key] if a.start_frame not in start_frames]
            self.annotations[key].append(merged_annotation)
            self.annotations[key].sort(key=lambda a: a.start_frame)
            
            # 记录操作历史
            self._add_to_history(action)
            
            # 更新统计
            self._update_stats()
            
            return True
            
        except Exception as e:
            print(f"合并注释失败: {e}")
            return False
    
    def split_annotation(self, key: str, start_frame: int, split_frame: int, 
                        first_desc: str = "", second_desc: str = "") -> bool:
        """分割注释"""
        try:
            # 查找要分割的注释
            annotation = self.find_annotation(key, start_frame)
            if not annotation:
                return False
            
            if not annotation.contains_frame(split_frame):
                return False
            
            if split_frame == annotation.start_frame or split_frame == annotation.end_frame:
                return False
            
            # 创建两个新注释
            first_annotation = Annotation(
                key=key,
                start_frame=annotation.start_frame,
                end_frame=split_frame - 1,
                description=first_desc or annotation.description,
                confidence=annotation.confidence,
                tags=annotation.tags.copy()
            )
            
            second_annotation = Annotation(
                key=key,
                start_frame=split_frame,
                end_frame=annotation.end_frame,
                description=second_desc or annotation.description,
                confidence=annotation.confidence,
                tags=annotation.tags.copy()
            )
            
            # 记录操作
            action = AnnotationAction(
                action_type='split',
                timestamp=datetime.now(),
                key=key,
                old_data=annotation.to_dict(),
                new_data=[first_annotation.to_dict(), second_annotation.to_dict()],
                frame_range=(annotation.start_frame, annotation.end_frame),
                description=f"分割注释在帧 {split_frame}"
            )
            
            # 删除原注释并添加新注释
            self.annotations[key] = [a for a in self.annotations[key] if a.start_frame != start_frame]
            self.annotations[key].extend([first_annotation, second_annotation])
            self.annotations[key].sort(key=lambda a: a.start_frame)
            
            # 记录操作历史
            self._add_to_history(action)
            
            # 更新统计
            self._update_stats()
            
            return True
            
        except Exception as e:
            print(f"分割注释失败: {e}")
            return False
    
    def find_annotation(self, key: str, start_frame: int) -> Optional[Annotation]:
        """查找注释"""
        if key not in self.annotations:
            return None
        
        for annotation in self.annotations[key]:
            if annotation.start_frame == start_frame:
                return annotation
        
        return None
    
    def get_annotations_at_frame(self, key: str, frame: int) -> List[Annotation]:
        """获取指定帧的所有注释"""
        if key not in self.annotations:
            return []
        
        return [ann for ann in self.annotations[key] if ann.contains_frame(frame)]
    
    def get_all_annotations(self, key: str = None) -> List[Annotation]:
        """获取所有注释"""
        if key:
            return self.annotations.get(key, []).copy()
        
        all_annotations = []
        for annotations in self.annotations.values():
            all_annotations.extend(annotations)
        
        return all_annotations
    
    def validate_annotation(self, annotation: Annotation) -> bool:
        """验证注释"""
        # 基本验证
        if annotation.start_frame < 0 or annotation.end_frame < 0:
            return False
        
        if annotation.start_frame > annotation.end_frame:
            return False
        
        if not annotation.description.strip():
            return False
        
        # 应用自定义验证规则
        for rule in self.validation_rules:
            if not rule(annotation):
                return False
        
        return True
    
    def add_validation_rule(self, rule_func):
        """添加验证规则"""
        self.validation_rules.append(rule_func)
    
    def undo(self) -> bool:
        """撤销操作"""
        if self.history_index < 0 or self.history_index >= len(self.action_history):
            return False
        
        action = self.action_history[self.history_index]
        success = self._undo_action(action)
        
        if success:
            self.history_index -= 1
            self._update_stats()
        
        return success
    
    def redo(self) -> bool:
        """重做操作"""
        if self.history_index + 1 >= len(self.action_history):
            return False
        
        self.history_index += 1
        action = self.action_history[self.history_index]
        success = self._redo_action(action)
        
        if not success:
            self.history_index -= 1
        else:
            self._update_stats()
        
        return success
    
    def _add_to_history(self, action: AnnotationAction):
        """添加到历史记录"""
        # 删除当前位置之后的历史
        self.action_history = self.action_history[:self.history_index + 1]
        
        # 添加新操作
        self.action_history.append(action)
        self.history_index = len(self.action_history) - 1
        
        # 限制历史长度
        if len(self.action_history) > self.max_history:
            self.action_history = self.action_history[-self.max_history:]
            self.history_index = len(self.action_history) - 1
    
    def _undo_action(self, action: AnnotationAction) -> bool:
        """撤销单个操作"""
        try:
            if action.action_type == 'add':
                # 撤销添加：删除注释
                if action.key in self.annotations:
                    self.annotations[action.key] = [
                        a for a in self.annotations[action.key] 
                        if not (a.start_frame == action.new_data['start_frame'])
                    ]
            
            elif action.action_type == 'edit':
                # 撤销编辑：恢复原注释
                old_annotation = Annotation.from_dict(action.old_data)
                if action.key in self.annotations:
                    for i, ann in enumerate(self.annotations[action.key]):
                        if ann.start_frame == action.new_data['start_frame']:
                            self.annotations[action.key][i] = old_annotation
                            break
            
            elif action.action_type == 'delete':
                # 撤销删除：恢复注释
                restored_annotation = Annotation.from_dict(action.old_data)
                if action.key not in self.annotations:
                    self.annotations[action.key] = []
                self.annotations[action.key].append(restored_annotation)
                self.annotations[action.key].sort(key=lambda a: a.start_frame)
            
            # 其他操作类型的撤销逻辑...
            
            return True
            
        except Exception as e:
            print(f"撤销操作失败: {e}")
            return False
    
    def _redo_action(self, action: AnnotationAction) -> bool:
        """重做单个操作"""
        try:
            if action.action_type == 'add':
                # 重做添加
                new_annotation = Annotation.from_dict(action.new_data)
                if action.key not in self.annotations:
                    self.annotations[action.key] = []
                self.annotations[action.key].append(new_annotation)
                self.annotations[action.key].sort(key=lambda a: a.start_frame)
            
            elif action.action_type == 'edit':
                # 重做编辑
                new_annotation = Annotation.from_dict(action.new_data)
                if action.key in self.annotations:
                    for i, ann in enumerate(self.annotations[action.key]):
                        if ann.start_frame == action.old_data['start_frame']:
                            self.annotations[action.key][i] = new_annotation
                            break
            
            elif action.action_type == 'delete':
                # 重做删除
                if action.key in self.annotations:
                    self.annotations[action.key] = [
                        a for a in self.annotations[action.key] 
                        if a.start_frame != action.old_data['start_frame']
                    ]
            
            # 其他操作类型的重做逻辑...
            
            return True
            
        except Exception as e:
            print(f"重做操作失败: {e}")
            return False
    
    def _update_stats(self):
        """更新统计信息"""
        all_annotations = self.get_all_annotations()
        
        self.stats['total_annotations'] = len(all_annotations)
        self.stats['keys_count'] = len(self.annotations)
        
        if all_annotations:
            total_duration = sum(ann.get_duration() for ann in all_annotations)
            self.stats['total_duration'] = total_duration
            self.stats['avg_duration'] = total_duration / len(all_annotations)
        else:
            self.stats['total_duration'] = 0
            self.stats['avg_duration'] = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def export_annotations(self, key: str = None) -> Dict[str, Any]:
        """导出注释"""
        if key:
            annotations = self.annotations.get(key, [])
            return {
                'key': key,
                'annotations': [ann.to_dict() for ann in annotations],
                'count': len(annotations)
            }
        else:
            return {
                'all_keys': {k: [ann.to_dict() for ann in v] for k, v in self.annotations.items()},
                'stats': self.stats,
                'export_time': datetime.now().isoformat()
            }
    
    def import_annotations(self, data: Dict[str, Any], merge: bool = False):
        """导入注释"""
        try:
            if 'all_keys' in data:
                # 导入所有键的数据
                for key, annotations_data in data['all_keys'].items():
                    if not merge:
                        self.annotations[key] = []
                    
                    for ann_data in annotations_data:
                        annotation = Annotation.from_dict(ann_data)
                        if self.validate_annotation(annotation):
                            if key not in self.annotations:
                                self.annotations[key] = []
                            self.annotations[key].append(annotation)
                    
                    if key in self.annotations:
                        self.annotations[key].sort(key=lambda a: a.start_frame)
            
            elif 'key' in data and 'annotations' in data:
                # 导入单个键的数据
                key = data['key']
                if not merge:
                    self.annotations[key] = []
                
                for ann_data in data['annotations']:
                    annotation = Annotation.from_dict(ann_data)
                    if self.validate_annotation(annotation):
                        if key not in self.annotations:
                            self.annotations[key] = []
                        self.annotations[key].append(annotation)
                
                if key in self.annotations:
                    self.annotations[key].sort(key=lambda a: a.start_frame)
            
            self._update_stats()
            return True
            
        except Exception as e:
            print(f"导入注释失败: {e}")
            return False
