#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import QTextEdit, QCompleter, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QWidget
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QStringListModel
from PyQt5.QtGui import QTextCursor, QKeySequence, QFont
from typing import List, Optional
import re


class SmartTextInput(QTextEdit):
    """智能文本输入框，支持自动完成、智能建议和快速输入"""
    
    # 信号
    suggestionSelected = pyqtSignal(str)  # 建议被选中
    confidenceChanged = pyqtSignal(float)  # 置信度变化
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 自动完成
        self.completer = None
        self.completion_model = QStringListModel()
        
        # 智能建议
        self.suggestions = []
        self.current_suggestion_index = -1
        
        # 输入历史
        self.input_history = []
        self.history_index = -1
        
        # 实时分析
        self.analysis_timer = QTimer()
        self.analysis_timer.setSingleShot(True)
        self.analysis_timer.timeout.connect(self.analyze_input)
        
        # 设置
        self.min_completion_length = 2
        self.max_suggestions = 5
        self.auto_suggest_delay = 500  # 毫秒
        
        # 连接信号
        self.textChanged.connect(self.on_text_changed)
        
        # 设置样式
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                font-size: 12px;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #4CAF50;
            }
        """)
    
    def set_completion_data(self, data: List[str]):
        """设置自动完成数据"""
        self.completion_model.setStringList(data)
        
        if self.completer:
            self.completer.deleteLater()
        
        self.completer = QCompleter(self.completion_model, self)
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.completer.setFilterMode(Qt.MatchContains)
        self.completer.setWidget(self)
        self.completer.activated.connect(self.insert_completion)
    
    def set_suggestions(self, suggestions: List[str]):
        """设置智能建议"""
        self.suggestions = suggestions[:self.max_suggestions]
        self.current_suggestion_index = -1
    
    def add_to_history(self, text: str):
        """添加到输入历史"""
        if text and text.strip():
            text = text.strip()
            if text in self.input_history:
                self.input_history.remove(text)
            self.input_history.insert(0, text)
            self.input_history = self.input_history[:50]  # 限制历史长度
    
    def on_text_changed(self):
        """文本变化处理"""
        # 重启分析定时器
        self.analysis_timer.stop()
        self.analysis_timer.start(self.auto_suggest_delay)
        
        # 更新自动完成
        text = self.toPlainText()
        if len(text) >= self.min_completion_length:
            if self.completer:
                self.completer.setCompletionPrefix(text)
                if self.completer.completionCount() > 0:
                    self.completer.complete()
    
    def analyze_input(self):
        """分析输入内容"""
        text = self.toPlainText().strip()
        if not text:
            return
        
        # 计算置信度
        confidence = self.calculate_confidence(text)
        self.confidenceChanged.emit(confidence)
        
        # 生成建议
        suggestions = self.generate_suggestions(text)
        self.set_suggestions(suggestions)
    
    def calculate_confidence(self, text: str) -> float:
        """计算输入置信度"""
        if not text:
            return 0.0
        
        confidence = 0.0
        
        # 长度因子
        length_factor = min(1.0, len(text) / 20)
        confidence += length_factor * 0.4
        
        # 完整性因子（是否包含动词、名词等）
        words = text.lower().split()
        if len(words) >= 2:
            confidence += 0.3
        
        # 语法因子（简单检查）
        if re.search(r'[.!?]$', text):  # 以标点结尾
            confidence += 0.1
        
        if not re.search(r'[^\w\s]', text):  # 没有特殊字符
            confidence += 0.1
        
        # 历史匹配因子
        if text in self.input_history:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def generate_suggestions(self, text: str) -> List[str]:
        """生成智能建议"""
        suggestions = []
        
        # 基于历史的建议
        for history_item in self.input_history:
            if text.lower() in history_item.lower() and history_item != text:
                suggestions.append(history_item)
        
        # 基于完成数据的建议
        if self.completer:
            model = self.completer.model()
            for i in range(model.rowCount()):
                item = model.data(model.index(i, 0))
                if text.lower() in item.lower() and item not in suggestions:
                    suggestions.append(item)
        
        return suggestions[:self.max_suggestions]
    
    def insert_completion(self, completion: str):
        """插入自动完成"""
        cursor = self.textCursor()
        cursor.select(QTextCursor.Document)
        cursor.insertText(completion)
        self.setTextCursor(cursor)
    
    def keyPressEvent(self, event):
        """处理按键事件"""
        # Tab键：接受第一个建议
        if event.key() == Qt.Key_Tab and self.suggestions:
            self.insert_completion(self.suggestions[0])
            self.suggestionSelected.emit(self.suggestions[0])
            return
        
        # Ctrl+Space：显示自动完成
        if event.key() == Qt.Key_Space and event.modifiers() & Qt.ControlModifier:
            if self.completer:
                self.completer.complete()
            return
        
        # 上下箭头：浏览历史
        if event.key() == Qt.Key_Up and event.modifiers() & Qt.ControlModifier:
            self.browse_history(-1)
            return
        elif event.key() == Qt.Key_Down and event.modifiers() & Qt.ControlModifier:
            self.browse_history(1)
            return
        
        # Escape：清除建议
        if event.key() == Qt.Key_Escape:
            self.suggestions.clear()
            if self.completer:
                self.completer.popup().hide()
            return
        
        super().keyPressEvent(event)
    
    def browse_history(self, direction: int):
        """浏览输入历史"""
        if not self.input_history:
            return
        
        self.history_index += direction
        self.history_index = max(-1, min(len(self.input_history) - 1, self.history_index))
        
        if self.history_index >= 0:
            self.setText(self.input_history[self.history_index])
        else:
            self.clear()
    
    def get_suggestions(self) -> List[str]:
        """获取当前建议"""
        return self.suggestions.copy()
    
    def clear_suggestions(self):
        """清除建议"""
        self.suggestions.clear()
        self.current_suggestion_index = -1
    
    def set_placeholder_text(self, text: str):
        """设置占位符文本"""
        self.setPlaceholderText(text)
    
    def get_confidence(self) -> float:
        """获取当前输入的置信度"""
        return self.calculate_confidence(self.toPlainText())


class SmartInputWidget(QWidget):
    """智能输入组件，包含输入框和建议面板"""
    
    textAccepted = pyqtSignal(str)  # 文本被接受
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 输入框
        self.text_input = SmartTextInput()
        layout.addWidget(self.text_input)
        
        # 建议面板
        self.suggestions_widget = QWidget()
        self.suggestions_layout = QVBoxLayout(self.suggestions_widget)
        self.suggestions_layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.suggestions_widget)
        
        # 置信度指示器
        confidence_layout = QHBoxLayout()
        self.confidence_label = QLabel("置信度:")
        self.confidence_value = QLabel("0%")
        confidence_layout.addWidget(self.confidence_label)
        confidence_layout.addWidget(self.confidence_value)
        confidence_layout.addStretch()
        layout.addLayout(confidence_layout)
        
        self.suggestions_widget.hide()
    
    def connect_signals(self):
        """连接信号"""
        self.text_input.suggestionSelected.connect(self.on_suggestion_selected)
        self.text_input.confidenceChanged.connect(self.update_confidence)
        self.text_input.textChanged.connect(self.update_suggestions_display)
    
    def on_suggestion_selected(self, suggestion: str):
        """建议被选中"""
        self.textAccepted.emit(suggestion)
    
    def update_confidence(self, confidence: float):
        """更新置信度显示"""
        percentage = int(confidence * 100)
        self.confidence_value.setText(f"{percentage}%")
        
        # 根据置信度设置颜色
        if confidence >= 0.8:
            color = "green"
        elif confidence >= 0.5:
            color = "orange"
        else:
            color = "red"
        
        self.confidence_value.setStyleSheet(f"color: {color}; font-weight: bold;")
    
    def update_suggestions_display(self):
        """更新建议显示"""
        # 清除现有建议
        for i in reversed(range(self.suggestions_layout.count())):
            self.suggestions_layout.itemAt(i).widget().setParent(None)
        
        suggestions = self.text_input.get_suggestions()
        
        if suggestions:
            self.suggestions_widget.show()
            for i, suggestion in enumerate(suggestions):
                btn = QPushButton(f"{i+1}. {suggestion}")
                btn.clicked.connect(lambda checked, s=suggestion: self.text_input.insert_completion(s))
                btn.setMaximumHeight(25)
                self.suggestions_layout.addWidget(btn)
        else:
            self.suggestions_widget.hide()
    
    def set_completion_data(self, data: List[str]):
        """设置自动完成数据"""
        self.text_input.set_completion_data(data)
    
    def get_text(self) -> str:
        """获取文本"""
        return self.text_input.toPlainText()
    
    def set_text(self, text: str):
        """设置文本"""
        self.text_input.setText(text)
    
    def clear(self):
        """清除内容"""
        self.text_input.clear()
        self.text_input.clear_suggestions()
