#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLabel, QLineEdit,
                             QComboBox, QCheckBox, QGroupBox, QTextEdit,
                             QProgressBar, QMessageBox, QHeaderView, QMenu,
                             QSpinBox, QDoubleSpinBox, QTabWidget, QWidget,
                             QSplitter, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QFont, QColor, QBrush
from typing import List, Dict, Any, Optional
from src.core.annotation_manager import Annotation, AnnotationManager
import re


class BulkEditWorker(QThread):
    """批量编辑工作线程"""

    progress = pyqtSignal(int)  # 进度信号
    finished = pyqtSignal(bool, str)  # 完成信号 (成功, 消息)

    def __init__(self, annotation_manager: AnnotationManager, operations: List[Dict[str, Any]]):
        super().__init__()
        self.annotation_manager = annotation_manager
        self.operations = operations
        self.should_stop = False

    def run(self):
        """执行批量操作"""
        try:
            total_ops = len(self.operations)
            success_count = 0

            for i, operation in enumerate(self.operations):
                if self.should_stop:
                    break

                # 执行操作
                if self.execute_operation(operation):
                    success_count += 1

                # 更新进度
                progress = int((i + 1) / total_ops * 100)
                self.progress.emit(progress)

            if self.should_stop:
                self.finished.emit(False, "操作被用户取消")
            else:
                self.finished.emit(True, f"成功执行 {success_count}/{total_ops} 个操作")

        except Exception as e:
            self.finished.emit(False, f"批量操作失败: {str(e)}")

    def execute_operation(self, operation: Dict[str, Any]) -> bool:
        """执行单个操作"""
        try:
            op_type = operation['type']

            if op_type == 'edit_description':
                return self.annotation_manager.edit_annotation(
                    operation['key'],
                    operation['start_frame'],
                    operation['new_annotation']
                )
            elif op_type == 'delete':
                return self.annotation_manager.delete_annotation(
                    operation['key'],
                    operation['start_frame']
                )
            elif op_type == 'merge':
                return self.annotation_manager.merge_annotations(
                    operation['key'],
                    operation['start_frames'],
                    operation.get('new_description', '')
                )
            elif op_type == 'split':
                return self.annotation_manager.split_annotation(
                    operation['key'],
                    operation['start_frame'],
                    operation['split_frame'],
                    operation.get('first_desc', ''),
                    operation.get('second_desc', '')
                )

            return False

        except Exception as e:
            print(f"执行操作失败: {e}")
            return False

    def stop(self):
        """停止操作"""
        self.should_stop = True


class BulkAnnotationEditor(QDialog):
    """批量注释编辑器"""

    annotationsChanged = pyqtSignal()  # 注释变化信号

    def __init__(self, annotation_manager: AnnotationManager, parent=None):
        super().__init__(parent)
        self.annotation_manager = annotation_manager
        self.selected_annotations = []
        self.filtered_annotations = []

        self.setWindowTitle("批量注释编辑器")
        self.setModal(True)
        self.resize(1000, 700)

        self.setup_ui()
        self.connect_signals()
        self.load_annotations()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 注释列表选项卡
        self.setup_annotation_list_tab()

        # 批量编辑选项卡
        self.setup_bulk_edit_tab()

        # 统计信息选项卡
        self.setup_statistics_tab()

        # 按钮栏
        button_layout = QHBoxLayout()

        self.apply_btn = QPushButton("应用更改")
        self.apply_btn.clicked.connect(self.apply_changes)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_annotations)

        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)

    def setup_annotation_list_tab(self):
        """设置注释列表选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 过滤器
        filter_group = QGroupBox("过滤器")
        filter_layout = QHBoxLayout(filter_group)

        filter_layout.addWidget(QLabel("键:"))
        self.key_filter = QComboBox()
        self.key_filter.setEditable(True)
        self.key_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.key_filter)

        filter_layout.addWidget(QLabel("描述包含:"))
        self.desc_filter = QLineEdit()
        self.desc_filter.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.desc_filter)

        filter_layout.addWidget(QLabel("最小持续时间:"))
        self.min_duration_filter = QSpinBox()
        self.min_duration_filter.setMinimum(0)
        self.min_duration_filter.setMaximum(10000)
        self.min_duration_filter.valueChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.min_duration_filter)

        layout.addWidget(filter_group)

        # 注释表格
        self.annotation_table = QTableWidget()
        self.annotation_table.setColumnCount(7)
        self.annotation_table.setHorizontalHeaderLabels([
            "选择", "键", "起始帧", "结束帧", "持续时间", "描述", "置信度"
        ])

        # 设置表格属性
        header = self.annotation_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # 描述列自动拉伸

        self.annotation_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.annotation_table.setAlternatingRowColors(True)
        self.annotation_table.setSortingEnabled(True)

        layout.addWidget(self.annotation_table)

        # 选择操作
        selection_layout = QHBoxLayout()

        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_annotations)

        self.select_none_btn = QPushButton("取消全选")
        self.select_none_btn.clicked.connect(self.select_no_annotations)

        self.invert_selection_btn = QPushButton("反选")
        self.invert_selection_btn.clicked.connect(self.invert_selection)

        selection_layout.addWidget(self.select_all_btn)
        selection_layout.addWidget(self.select_none_btn)
        selection_layout.addWidget(self.invert_selection_btn)
        selection_layout.addStretch()

        self.selected_count_label = QLabel("已选择: 0 个注释")
        selection_layout.addWidget(self.selected_count_label)

        layout.addLayout(selection_layout)

        self.tab_widget.addTab(tab, "注释列表")

    def setup_bulk_edit_tab(self):
        """设置批量编辑选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 编辑操作
        edit_group = QGroupBox("批量编辑操作")
        edit_layout = QVBoxLayout(edit_group)

        # 描述替换
        desc_replace_layout = QHBoxLayout()
        desc_replace_layout.addWidget(QLabel("替换描述:"))

        self.find_text = QLineEdit()
        self.find_text.setPlaceholderText("查找文本...")
        desc_replace_layout.addWidget(self.find_text)

        desc_replace_layout.addWidget(QLabel("替换为:"))

        self.replace_text = QLineEdit()
        self.replace_text.setPlaceholderText("替换文本...")
        desc_replace_layout.addWidget(self.replace_text)

        self.regex_checkbox = QCheckBox("使用正则表达式")
        desc_replace_layout.addWidget(self.regex_checkbox)

        self.preview_replace_btn = QPushButton("预览")
        self.preview_replace_btn.clicked.connect(self.preview_replace)
        desc_replace_layout.addWidget(self.preview_replace_btn)

        edit_layout.addLayout(desc_replace_layout)

        # 置信度调整
        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(QLabel("设置置信度:"))

        self.confidence_spinbox = QDoubleSpinBox()
        self.confidence_spinbox.setRange(0.0, 1.0)
        self.confidence_spinbox.setSingleStep(0.1)
        self.confidence_spinbox.setValue(1.0)
        confidence_layout.addWidget(self.confidence_spinbox)

        self.set_confidence_btn = QPushButton("应用置信度")
        self.set_confidence_btn.clicked.connect(self.set_confidence)
        confidence_layout.addWidget(self.set_confidence_btn)

        confidence_layout.addStretch()
        edit_layout.addLayout(confidence_layout)

        # 标签操作
        tags_layout = QHBoxLayout()
        tags_layout.addWidget(QLabel("添加标签:"))

        self.new_tag_input = QLineEdit()
        self.new_tag_input.setPlaceholderText("输入标签...")
        tags_layout.addWidget(self.new_tag_input)

        self.add_tag_btn = QPushButton("添加标签")
        self.add_tag_btn.clicked.connect(self.add_tag)
        tags_layout.addWidget(self.add_tag_btn)

        tags_layout.addStretch()
        edit_layout.addLayout(tags_layout)

        layout.addWidget(edit_group)

        # 批量操作
        batch_group = QGroupBox("批量操作")
        batch_layout = QVBoxLayout(batch_group)

        batch_buttons_layout = QHBoxLayout()

        self.merge_selected_btn = QPushButton("合并选中")
        self.merge_selected_btn.clicked.connect(self.merge_selected)
        batch_buttons_layout.addWidget(self.merge_selected_btn)

        self.delete_selected_btn = QPushButton("删除选中")
        self.delete_selected_btn.clicked.connect(self.delete_selected)
        batch_buttons_layout.addWidget(self.delete_selected_btn)

        self.duplicate_selected_btn = QPushButton("复制选中")
        self.duplicate_selected_btn.clicked.connect(self.duplicate_selected)
        batch_buttons_layout.addWidget(self.duplicate_selected_btn)

        batch_buttons_layout.addStretch()
        batch_layout.addLayout(batch_buttons_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        batch_layout.addWidget(self.progress_bar)

        layout.addWidget(batch_group)

        # 预览区域
        preview_group = QGroupBox("操作预览")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)

        layout.addWidget(preview_group)

        layout.addStretch()

        self.tab_widget.addTab(tab, "批量编辑")

    def setup_statistics_tab(self):
        """设置统计信息选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 基本统计
        stats_group = QGroupBox("统计信息")
        stats_layout = QVBoxLayout(stats_group)

        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(200)
        stats_layout.addWidget(self.stats_text)

        layout.addWidget(stats_group)

        # 键分布
        key_dist_group = QGroupBox("键分布")
        key_dist_layout = QVBoxLayout(key_dist_group)

        self.key_dist_list = QListWidget()
        key_dist_layout.addWidget(self.key_dist_list)

        layout.addWidget(key_dist_group)

        layout.addStretch()

        self.tab_widget.addTab(tab, "统计信息")

    def connect_signals(self):
        """连接信号"""
        self.annotation_table.itemChanged.connect(self.on_table_item_changed)
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def load_annotations(self):
        """加载注释"""
        # 获取所有注释
        all_annotations = self.annotation_manager.get_all_annotations()
        self.filtered_annotations = all_annotations.copy()

        # 更新键过滤器
        keys = list(set(ann.key for ann in all_annotations))
        self.key_filter.clear()
        self.key_filter.addItem("所有键")
        self.key_filter.addItems(sorted(keys))

        # 应用过滤器
        self.apply_filters()

        # 更新统计信息
        self.update_statistics()

    def apply_filters(self):
        """应用过滤器"""
        all_annotations = self.annotation_manager.get_all_annotations()
        filtered = []

        key_filter = self.key_filter.currentText()
        desc_filter = self.desc_filter.text().lower()
        min_duration = self.min_duration_filter.value()

        for ann in all_annotations:
            # 键过滤
            if key_filter != "所有键" and ann.key != key_filter:
                continue

            # 描述过滤
            if desc_filter and desc_filter not in ann.description.lower():
                continue

            # 持续时间过滤
            if ann.get_duration() < min_duration:
                continue

            filtered.append(ann)

        self.filtered_annotations = filtered
        self.update_annotation_table()

    def update_annotation_table(self):
        """更新注释表格"""
        self.annotation_table.setRowCount(len(self.filtered_annotations))

        for row, annotation in enumerate(self.filtered_annotations):
            # 选择复选框
            checkbox = QCheckBox()
            checkbox.stateChanged.connect(self.update_selection_count)
            self.annotation_table.setCellWidget(row, 0, checkbox)

            # 其他列
            self.annotation_table.setItem(row, 1, QTableWidgetItem(annotation.key))
            self.annotation_table.setItem(row, 2, QTableWidgetItem(str(annotation.start_frame)))
            self.annotation_table.setItem(row, 3, QTableWidgetItem(str(annotation.end_frame)))
            self.annotation_table.setItem(row, 4, QTableWidgetItem(str(annotation.get_duration())))
            self.annotation_table.setItem(row, 5, QTableWidgetItem(annotation.description))
            self.annotation_table.setItem(row, 6, QTableWidgetItem(f"{annotation.confidence:.2f}"))

            # 根据置信度设置颜色
            if annotation.confidence < 0.5:
                color = QColor(255, 200, 200)  # 红色
            elif annotation.confidence < 0.8:
                color = QColor(255, 255, 200)  # 黄色
            else:
                color = QColor(200, 255, 200)  # 绿色

            for col in range(1, 7):
                item = self.annotation_table.item(row, col)
                if item:
                    item.setBackground(QBrush(color))

        self.update_selection_count()

    def update_selection_count(self):
        """更新选择计数"""
        count = 0
        for row in range(self.annotation_table.rowCount()):
            checkbox = self.annotation_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                count += 1

        self.selected_count_label.setText(f"已选择: {count} 个注释")

    def get_selected_annotations(self) -> List[Annotation]:
        """获取选中的注释"""
        selected = []
        for row in range(self.annotation_table.rowCount()):
            checkbox = self.annotation_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                if row < len(self.filtered_annotations):
                    selected.append(self.filtered_annotations[row])
        return selected

    def select_all_annotations(self):
        """全选注释"""
        for row in range(self.annotation_table.rowCount()):
            checkbox = self.annotation_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def select_no_annotations(self):
        """取消全选"""
        for row in range(self.annotation_table.rowCount()):
            checkbox = self.annotation_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def invert_selection(self):
        """反选"""
        for row in range(self.annotation_table.rowCount()):
            checkbox = self.annotation_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(not checkbox.isChecked())

    def preview_replace(self):
        """预览替换操作"""
        find_text = self.find_text.text()
        replace_text = self.replace_text.text()
        use_regex = self.regex_checkbox.isChecked()

        if not find_text:
            QMessageBox.warning(self, "警告", "请输入要查找的文本")
            return

        selected = self.get_selected_annotations()
        if not selected:
            QMessageBox.warning(self, "警告", "请选择要操作的注释")
            return

        preview_lines = []
        for ann in selected:
            old_desc = ann.description

            try:
                if use_regex:
                    new_desc = re.sub(find_text, replace_text, old_desc)
                else:
                    new_desc = old_desc.replace(find_text, replace_text)

                if old_desc != new_desc:
                    preview_lines.append(f"帧 {ann.start_frame}-{ann.end_frame}:")
                    preview_lines.append(f"  原文: {old_desc}")
                    preview_lines.append(f"  新文: {new_desc}")
                    preview_lines.append("")

            except re.error as e:
                preview_lines.append(f"正则表达式错误: {e}")
                break

        if not preview_lines:
            preview_lines = ["没有找到匹配的文本"]

        self.preview_text.setPlainText("\n".join(preview_lines))

    def set_confidence(self):
        """设置置信度"""
        selected = self.get_selected_annotations()
        if not selected:
            QMessageBox.warning(self, "警告", "请选择要操作的注释")
            return

        confidence = self.confidence_spinbox.value()

        for ann in selected:
            ann.confidence = confidence

        self.update_annotation_table()
        QMessageBox.information(self, "成功", f"已设置 {len(selected)} 个注释的置信度为 {confidence}")

    def add_tag(self):
        """添加标签"""
        tag = self.new_tag_input.text().strip()
        if not tag:
            QMessageBox.warning(self, "警告", "请输入标签")
            return

        selected = self.get_selected_annotations()
        if not selected:
            QMessageBox.warning(self, "警告", "请选择要操作的注释")
            return

        for ann in selected:
            if tag not in ann.tags:
                ann.tags.append(tag)

        self.new_tag_input.clear()
        QMessageBox.information(self, "成功", f"已为 {len(selected)} 个注释添加标签 '{tag}'")

    def merge_selected(self):
        """合并选中的注释"""
        selected = self.get_selected_annotations()
        if len(selected) < 2:
            QMessageBox.warning(self, "警告", "请选择至少2个注释进行合并")
            return

        # 检查是否为同一个键
        keys = set(ann.key for ann in selected)
        if len(keys) > 1:
            QMessageBox.warning(self, "警告", "只能合并相同键的注释")
            return

        key = list(keys)[0]
        start_frames = [ann.start_frame for ann in selected]

        # 询问合并后的描述
        from PyQt5.QtWidgets import QInputDialog
        new_desc, ok = QInputDialog.getText(
            self, "合并注释", "请输入合并后的描述:",
            text="; ".join(ann.description for ann in selected)
        )

        if ok:
            success = self.annotation_manager.merge_annotations(key, start_frames, new_desc)
            if success:
                self.load_annotations()
                QMessageBox.information(self, "成功", "注释合并成功")
            else:
                QMessageBox.warning(self, "失败", "注释合并失败")

    def delete_selected(self):
        """删除选中的注释"""
        selected = self.get_selected_annotations()
        if not selected:
            QMessageBox.warning(self, "警告", "请选择要删除的注释")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除 {len(selected)} 个注释吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            success_count = 0
            for ann in selected:
                if self.annotation_manager.delete_annotation(ann.key, ann.start_frame):
                    success_count += 1

            self.load_annotations()
            QMessageBox.information(self, "完成", f"成功删除 {success_count}/{len(selected)} 个注释")

    def duplicate_selected(self):
        """复制选中的注释"""
        selected = self.get_selected_annotations()
        if not selected:
            QMessageBox.warning(self, "警告", "请选择要复制的注释")
            return

        # 询问偏移量
        from PyQt5.QtWidgets import QInputDialog
        offset, ok = QInputDialog.getInt(
            self, "复制注释", "请输入帧偏移量:",
            value=100, min=1, max=10000
        )

        if ok:
            success_count = 0
            for ann in selected:
                new_ann = Annotation(
                    key=ann.key,
                    start_frame=ann.start_frame + offset,
                    end_frame=ann.end_frame + offset,
                    description=f"{ann.description} (副本)",
                    confidence=ann.confidence,
                    tags=ann.tags.copy()
                )

                if self.annotation_manager.add_annotation(new_ann):
                    success_count += 1

            self.load_annotations()
            QMessageBox.information(self, "完成", f"成功复制 {success_count}/{len(selected)} 个注释")

    def update_statistics(self):
        """更新统计信息"""
        stats = self.annotation_manager.get_stats()
        all_annotations = self.annotation_manager.get_all_annotations()

        # 基本统计
        stats_text = f"""
总注释数: {stats['total_annotations']}
键数量: {stats['keys_count']}
总持续时间: {stats['total_duration']} 帧
平均持续时间: {stats['avg_duration']:.2f} 帧

置信度分布:
- 高 (≥0.8): {len([a for a in all_annotations if a.confidence >= 0.8])}
- 中 (0.5-0.8): {len([a for a in all_annotations if 0.5 <= a.confidence < 0.8])}
- 低 (<0.5): {len([a for a in all_annotations if a.confidence < 0.5])}
        """.strip()

        self.stats_text.setPlainText(stats_text)

        # 键分布
        key_counts = {}
        for ann in all_annotations:
            key_counts[ann.key] = key_counts.get(ann.key, 0) + 1

        self.key_dist_list.clear()
        for key, count in sorted(key_counts.items(), key=lambda x: x[1], reverse=True):
            item = QListWidgetItem(f"{key}: {count} 个注释")
            self.key_dist_list.addItem(item)

    def on_table_item_changed(self, item):
        """表格项变化处理"""
        # 这里可以处理直接在表格中编辑的情况
        pass

    def on_tab_changed(self, index):
        """选项卡变化处理"""
        if index == 2:  # 统计信息选项卡
            self.update_statistics()

    def apply_changes(self):
        """应用更改"""
        # 这里可以添加最终确认和应用逻辑
        self.annotationsChanged.emit()
        self.accept()