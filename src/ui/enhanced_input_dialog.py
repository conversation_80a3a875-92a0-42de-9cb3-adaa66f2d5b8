#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLineEdit,
                             QPushButton, QLabel, QListWidget, QListWidgetItem,
                             QTabWidget, QWidget, QTextEdit, QSplitter,
                             QGroupBox, QComboBox, QCheckBox, QSpacerItem,
                             QSizePolicy, QMessageBox, QInputDialog, QCompleter,
                             QShortcut, QProgressBar, QSlider)
from PyQt5.QtCore import Qt, pyqtSignal, QStringListModel, QTimer, QSettings
from PyQt5.QtGui import QFont, QPalette, QKeySequence, QTextCursor
from typing import Optional, List, Dict
from src.utils.phrase_library import PhraseLibrary
import json
import os

class EnhancedInputDialog(QDialog):
    """增强版Language输入对话框，支持手动输入、词库选择、自动完成和历史记录"""

    def __init__(self, parent=None, start_idx: int = 0, end_idx: int = 0,
                 current_key: str = "language", initial_text: str = ""):
        """
        初始化对话框

        Args:
            parent: 父窗口
            start_idx: 起始帧索引
            end_idx: 结束帧索引
            current_key: 当前编辑的键名
            initial_text: 初始文本（用于编辑已有内容）
        """
        super().__init__(parent)
        self.start_idx = start_idx
        self.end_idx = end_idx
        self.current_key = current_key
        self.initial_text = initial_text
        self.phrase_library = PhraseLibrary()
        self.selected_phrase = ""

        # 历史记录和自动完成
        self.settings = QSettings("HDF5Viewer", "AnnotationHistory")
        self.recent_annotations = self.load_recent_annotations()
        self.auto_complete_model = QStringListModel()
        self.completer = QCompleter()
        self.completer.setModel(self.auto_complete_model)
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.completer.setFilterMode(Qt.MatchContains)

        # 快速输入模式
        self.quick_input_mode = False
        self.confidence_threshold = 0.7

        # 输入统计
        self.input_stats = {"total_inputs": 0, "avg_length": 0, "common_words": []}

        self.setWindowTitle(f"设置 {current_key} 描述 (帧 {start_idx} - {end_idx})")
        self.setModal(True)
        self.resize(700, 600)  # 增大窗口以容纳新功能

        self.setup_ui()
        self.connect_signals()
        self.setup_auto_completion()
        self.setup_shortcuts()

        # 如果有初始文本，填充到输入框
        if initial_text:
            self.manual_input.setText(initial_text)

        # 加载输入统计
        self.load_input_stats()

    def load_recent_annotations(self) -> List[str]:
        """加载最近的注释历史"""
        try:
            recent_str = self.settings.value("recent_annotations", "[]")
            if isinstance(recent_str, str):
                return json.loads(recent_str)
            return recent_str if isinstance(recent_str, list) else []
        except:
            return []

    def save_recent_annotations(self):
        """保存最近的注释历史"""
        try:
            # 只保留最近的50个注释
            recent_limited = self.recent_annotations[-50:]
            self.settings.setValue("recent_annotations", json.dumps(recent_limited))
        except Exception as e:
            print(f"保存注释历史失败: {e}")

    def add_to_recent_annotations(self, text: str):
        """添加到最近注释列表"""
        if text and text.strip():
            text = text.strip()
            # 移除重复项
            if text in self.recent_annotations:
                self.recent_annotations.remove(text)
            # 添加到开头
            self.recent_annotations.insert(0, text)
            # 限制列表长度
            self.recent_annotations = self.recent_annotations[:50]
            self.save_recent_annotations()

    def setup_auto_completion(self):
        """设置自动完成功能"""
        # 收集所有可能的完成项
        completion_items = set()

        # 添加词库中的短语
        completion_items.update(self.phrase_library.get_all_phrases())

        # 添加最近的注释
        completion_items.update(self.recent_annotations)

        # 更新自动完成模型
        self.auto_complete_model.setStringList(sorted(completion_items))

        # 设置完成器
        self.manual_input.setCompleter(self.completer)

    def setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+1-9: 快速选择最近的注释
        for i in range(1, 10):
            if i <= len(self.recent_annotations):
                shortcut = QShortcut(QKeySequence(f"Ctrl+{i}"), self)
                shortcut.activated.connect(lambda idx=i-1: self.use_recent_annotation(idx))

        # Ctrl+Space: 显示自动完成
        auto_complete_shortcut = QShortcut(QKeySequence("Ctrl+Space"), self)
        auto_complete_shortcut.activated.connect(self.show_auto_complete)

        # Ctrl+H: 显示历史记录
        history_shortcut = QShortcut(QKeySequence("Ctrl+H"), self)
        history_shortcut.activated.connect(self.show_history_dialog)

        # F1: 显示帮助
        help_shortcut = QShortcut(QKeySequence("F1"), self)
        help_shortcut.activated.connect(self.show_help)

    def use_recent_annotation(self, index: int):
        """使用最近的注释"""
        if 0 <= index < len(self.recent_annotations):
            self.manual_input.setText(self.recent_annotations[index])
            self.update_preview()

    def show_auto_complete(self):
        """显示自动完成"""
        self.completer.complete()

    def show_history_dialog(self):
        """显示历史记录对话框"""
        if not self.recent_annotations:
            QMessageBox.information(self, "历史记录", "暂无历史记录")
            return

        # 创建简单的选择对话框
        from PyQt5.QtWidgets import QListWidget, QDialog, QVBoxLayout, QPushButton

        dialog = QDialog(self)
        dialog.setWindowTitle("注释历史记录")
        dialog.resize(400, 300)

        layout = QVBoxLayout(dialog)

        list_widget = QListWidget()
        for annotation in self.recent_annotations[:20]:  # 只显示最近20个
            list_widget.addItem(annotation)

        layout.addWidget(list_widget)

        button_layout = QHBoxLayout()
        use_btn = QPushButton("使用")
        cancel_btn = QPushButton("取消")

        button_layout.addWidget(use_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        def use_selected():
            current_item = list_widget.currentItem()
            if current_item:
                self.manual_input.setText(current_item.text())
                self.update_preview()
                dialog.accept()

        use_btn.clicked.connect(use_selected)
        cancel_btn.clicked.connect(dialog.reject)
        list_widget.itemDoubleClicked.connect(use_selected)

        dialog.exec_()

    def show_help(self):
        """显示帮助信息"""
        help_text = """
快捷键帮助：

Ctrl+1-9: 使用最近的注释 (1-9)
Ctrl+Space: 显示自动完成
Ctrl+H: 显示历史记录
Ctrl+Enter: 快速确认
F1: 显示此帮助

输入技巧：
- 输入时会自动显示匹配的建议
- 可以从词库中选择常用短语
- 系统会记住您的输入历史
        """
        QMessageBox.information(self, "帮助", help_text.strip())

    def load_input_stats(self):
        """加载输入统计信息"""
        try:
            stats_str = self.settings.value("input_stats", "{}")
            if isinstance(stats_str, str):
                self.input_stats = json.loads(stats_str)
            else:
                self.input_stats = stats_str if isinstance(stats_str, dict) else {}
        except:
            self.input_stats = {"total_inputs": 0, "avg_length": 0, "common_words": []}

    def save_input_stats(self):
        """保存输入统计信息"""
        try:
            self.settings.setValue("input_stats", json.dumps(self.input_stats))
        except Exception as e:
            print(f"保存输入统计失败: {e}")

    def update_input_stats(self, text: str):
        """更新输入统计"""
        if not text or not text.strip():
            return

        self.input_stats["total_inputs"] = self.input_stats.get("total_inputs", 0) + 1

        # 更新平均长度
        current_avg = self.input_stats.get("avg_length", 0)
        total_inputs = self.input_stats["total_inputs"]
        new_avg = (current_avg * (total_inputs - 1) + len(text)) / total_inputs
        self.input_stats["avg_length"] = new_avg

        # 更新常用词
        words = text.lower().split()
        common_words = self.input_stats.get("common_words", [])
        for word in words:
            if len(word) > 2:  # 只统计长度大于2的词
                found = False
                for i, (w, count) in enumerate(common_words):
                    if w == word:
                        common_words[i] = (w, count + 1)
                        found = True
                        break
                if not found:
                    common_words.append((word, 1))

        # 按频率排序并保留前20个
        common_words.sort(key=lambda x: x[1], reverse=True)
        self.input_stats["common_words"] = common_words[:20]

        self.save_input_stats()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 标题信息
        info_label = QLabel(f"为帧 {self.start_idx} 至 {self.end_idx} 设置 '{self.current_key}' 描述")
        info_label.setFont(QFont("Arial", 10, QFont.Bold))
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # 左侧：手动输入区域
        self.setup_manual_input_area(main_splitter)
        
        # 右侧：词库选择区域
        self.setup_phrase_library_area(main_splitter)
        
        # 设置分割器比例
        main_splitter.setSizes([250, 350])
        
        # 底部按钮区域
        self.setup_button_area(layout)
    
    def setup_manual_input_area(self, parent_widget):
        """设置手动输入区域，增强版本"""
        manual_group = QGroupBox("手动输入")
        manual_layout = QVBoxLayout(manual_group)

        # 输入框
        self.manual_input = QTextEdit()
        self.manual_input.setMaximumHeight(100)
        self.manual_input.setPlaceholderText("请输入描述文本... (支持自动完成)")
        manual_layout.addWidget(self.manual_input)

        # 快速输入工具栏
        quick_toolbar = QHBoxLayout()

        # 最近使用按钮
        recent_btn = QPushButton("最近使用")
        recent_btn.setToolTip("显示最近使用的注释 (Ctrl+H)")
        recent_btn.clicked.connect(self.show_history_dialog)
        quick_toolbar.addWidget(recent_btn)

        # 自动完成按钮
        auto_complete_btn = QPushButton("自动完成")
        auto_complete_btn.setToolTip("显示自动完成建议 (Ctrl+Space)")
        auto_complete_btn.clicked.connect(self.show_auto_complete)
        quick_toolbar.addWidget(auto_complete_btn)

        # 快速输入模式切换
        self.quick_mode_checkbox = QCheckBox("快速模式")
        self.quick_mode_checkbox.setToolTip("启用快速输入模式，减少确认步骤")
        self.quick_mode_checkbox.toggled.connect(self.toggle_quick_mode)
        quick_toolbar.addWidget(self.quick_mode_checkbox)

        manual_layout.addLayout(quick_toolbar)

        # 最近注释快速访问
        if self.recent_annotations:
            recent_group = QGroupBox("最近注释 (Ctrl+1-9)")
            recent_layout = QVBoxLayout(recent_group)
            recent_layout.setSpacing(2)

            for i, annotation in enumerate(self.recent_annotations[:9]):
                btn = QPushButton(f"{i+1}. {annotation[:30]}{'...' if len(annotation) > 30 else ''}")
                btn.setToolTip(annotation)
                btn.clicked.connect(lambda checked, idx=i: self.use_recent_annotation(idx))
                btn.setMaximumHeight(25)
                recent_layout.addWidget(btn)

            manual_layout.addWidget(recent_group)

        # 输入提示和统计
        info_layout = QHBoxLayout()

        tip_label = QLabel("支持多行输入，Ctrl+Enter快速确认")
        tip_label.setStyleSheet("color: #666; font-size: 10px;")
        info_layout.addWidget(tip_label)

        # 显示输入统计
        if self.input_stats.get("total_inputs", 0) > 0:
            stats_label = QLabel(f"已输入 {self.input_stats['total_inputs']} 次")
            stats_label.setStyleSheet("color: #666; font-size: 10px;")
            info_layout.addWidget(stats_label)

        manual_layout.addLayout(info_layout)

        # 添加当前输入到词库的按钮
        add_to_library_btn = QPushButton("添加到词库")
        add_to_library_btn.setToolTip("将当前输入的文本添加到词库中")
        add_to_library_btn.clicked.connect(self.add_current_text_to_library)
        manual_layout.addWidget(add_to_library_btn)

        # 添加弹簧
        manual_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        parent_widget.addWidget(manual_group)

    def toggle_quick_mode(self, enabled: bool):
        """切换快速输入模式"""
        self.quick_input_mode = enabled
        if enabled:
            self.setWindowTitle(f"快速设置 {self.current_key} 描述 (帧 {self.start_idx} - {self.end_idx})")
        else:
            self.setWindowTitle(f"设置 {self.current_key} 描述 (帧 {self.start_idx} - {self.end_idx})")
    
    def setup_phrase_library_area(self, parent_widget):
        """设置词库选择区域"""
        library_group = QGroupBox("词库选择")
        library_layout = QVBoxLayout(library_group)
        
        # 顶部控制区域
        control_layout = QHBoxLayout()
        
        # 分类选择
        category_label = QLabel("分类:")
        self.category_combo = QComboBox()
        self.category_combo.addItem("全部")
        categories = self.phrase_library.get_categories()
        for category in categories.keys():
            self.category_combo.addItem(category)
        
        control_layout.addWidget(category_label)
        control_layout.addWidget(self.category_combo)
        
        # 搜索框
        search_label = QLabel("搜索:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索...")
        
        control_layout.addWidget(search_label)
        control_layout.addWidget(self.search_input)
        
        library_layout.addLayout(control_layout)
        
        # 短语列表
        self.phrase_list = QListWidget()
        self.phrase_list.setAlternatingRowColors(True)
        self.populate_phrase_list()
        library_layout.addWidget(self.phrase_list)
        
        # 底部操作按钮
        library_btn_layout = QHBoxLayout()
        
        self.use_phrase_btn = QPushButton("使用选中短语")
        self.use_phrase_btn.setEnabled(False)
        
        edit_library_btn = QPushButton("管理词库")
        edit_library_btn.setToolTip("添加、删除或编辑词库中的短语")
        
        reload_library_btn = QPushButton("重新加载")
        reload_library_btn.setToolTip("重新加载词库文件")
        
        library_btn_layout.addWidget(self.use_phrase_btn)
        library_btn_layout.addWidget(edit_library_btn)
        library_btn_layout.addWidget(reload_library_btn)
        
        library_layout.addLayout(library_btn_layout)
        
        # 连接信号
        self.use_phrase_btn.clicked.connect(self.use_selected_phrase)
        edit_library_btn.clicked.connect(self.open_library_manager)
        reload_library_btn.clicked.connect(self.reload_phrase_library)
        
        parent_widget.addWidget(library_group)
    
    def setup_button_area(self, layout):
        """设置底部按钮区域"""
        button_layout = QHBoxLayout()
        
        # 预览区域
        preview_group = QGroupBox("当前选择预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("未选择任何内容")
        self.preview_label.setWordWrap(True)
        self.preview_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                min-height: 40px;
            }
        """)
        preview_layout.addWidget(self.preview_label)
        
        layout.addWidget(preview_group)
        
        # 主要操作按钮
        main_button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("确定")
        self.ok_button.setDefault(True)
        self.ok_button.setMinimumHeight(35)
        
        cancel_button = QPushButton("取消")
        cancel_button.setMinimumHeight(35)
        
        main_button_layout.addWidget(self.ok_button)
        main_button_layout.addWidget(cancel_button)
        
        layout.addLayout(main_button_layout)
        
        # 连接信号
        self.ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
    
    def connect_signals(self):
        """连接信号"""
        # 分类变化
        self.category_combo.currentTextChanged.connect(self.filter_phrases)
        
        # 搜索
        self.search_input.textChanged.connect(self.filter_phrases)
        
        # 列表选择
        self.phrase_list.itemSelectionChanged.connect(self.on_phrase_selection_changed)
        self.phrase_list.itemDoubleClicked.connect(self.on_phrase_double_clicked)
        
        # 手动输入变化
        self.manual_input.textChanged.connect(self.update_preview)
        
        # 词库更新
        self.phrase_library.library_updated.connect(self.on_library_updated)
        
        # 快捷键
        self.manual_input.keyPressEvent = self.manual_input_key_press
    
    def populate_phrase_list(self, phrases=None):
        """填充短语列表"""
        self.phrase_list.clear()
        
        if phrases is None:
            phrases = self.phrase_library.get_all_phrases()
        
        for phrase in phrases:
            item = QListWidgetItem(phrase)
            item.setToolTip(phrase)
            self.phrase_list.addItem(item)
    
    def filter_phrases(self):
        """根据分类和搜索条件过滤短语"""
        category = self.category_combo.currentText()
        search_text = self.search_input.text().strip()
        
        # 获取基础短语列表
        if category == "全部":
            phrases = self.phrase_library.get_all_phrases()
        else:
            phrases = self.phrase_library.get_phrases_by_category(category)
        
        # 应用搜索过滤
        if search_text:
            search_text = search_text.lower()
            phrases = [p for p in phrases if search_text in p.lower()]
        
        self.populate_phrase_list(phrases)
    
    def on_phrase_selection_changed(self):
        """处理短语选择变化"""
        selected_items = self.phrase_list.selectedItems()
        
        if selected_items:
            self.selected_phrase = selected_items[0].text()
            self.use_phrase_btn.setEnabled(True)
        else:
            self.selected_phrase = ""
            self.use_phrase_btn.setEnabled(False)
        
        self.update_preview()
    
    def on_phrase_double_clicked(self, item):
        """处理短语双击"""
        self.use_selected_phrase()
    
    def use_selected_phrase(self):
        """使用选中的短语"""
        if self.selected_phrase:
            self.manual_input.setText(self.selected_phrase)
            self.update_preview()
    
    def update_preview(self):
        """更新预览"""
        manual_text = self.manual_input.toPlainText().strip()
        
        if manual_text:
            self.preview_label.setText(f"将设置: {manual_text}")
            self.preview_label.setStyleSheet("""
                QLabel {
                    background-color: #e8f5e8;
                    border: 1px solid #4caf50;
                    border-radius: 4px;
                    padding: 8px;
                    min-height: 40px;
                    color: #2e7d32;
                }
            """)
        elif self.selected_phrase:
            self.preview_label.setText(f"已选择: {self.selected_phrase}")
            self.preview_label.setStyleSheet("""
                QLabel {
                    background-color: #e3f2fd;
                    border: 1px solid #2196f3;
                    border-radius: 4px;
                    padding: 8px;
                    min-height: 40px;
                    color: #1565c0;
                }
            """)
        else:
            self.preview_label.setText("请输入文本或选择词库中的短语")
            self.preview_label.setStyleSheet("""
                QLabel {
                    background-color: #f5f5f5;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 8px;
                    min-height: 40px;
                    color: #666;
                }
            """)
    
    def add_current_text_to_library(self):
        """添加当前输入的文本到词库"""
        text = self.manual_input.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请先输入要添加的文本")
            return
        
        # 询问分类
        categories = list(self.phrase_library.get_categories().keys())
        if not categories:
            categories = ["默认"]
        
        category, ok = QInputDialog.getItem(
            self, "选择分类", "请选择要添加到的分类:", 
            categories + ["新建分类..."], 0, False
        )
        
        if not ok:
            return
        
        if category == "新建分类...":
            category, ok = QInputDialog.getText(
                self, "新建分类", "请输入新分类名称:"
            )
            if not ok or not category.strip():
                return
            category = category.strip()
        
        # 添加到词库
        if self.phrase_library.add_phrase(text, category):
            QMessageBox.information(self, "成功", f"已将 '{text}' 添加到分类 '{category}'")
            self.filter_phrases()  # 刷新列表
        else:
            QMessageBox.warning(self, "失败", "该短语已存在于词库中")
    
    def open_library_manager(self):
        """打开词库管理器"""
        from src.ui.phrase_library_manager import PhraseLibraryManager
        manager = PhraseLibraryManager(self, self.phrase_library)
        manager.exec_()
    
    def reload_phrase_library(self):
        """重新加载词库"""
        self.phrase_library.reload_library()
        
        # 重新填充分类下拉框
        self.category_combo.clear()
        self.category_combo.addItem("全部")
        categories = self.phrase_library.get_categories()
        for category in categories.keys():
            self.category_combo.addItem(category)
        
        # 刷新列表
        self.filter_phrases()
        
        QMessageBox.information(self, "完成", "词库已重新加载")
    
    def on_library_updated(self):
        """词库更新时的处理"""
        current_category = self.category_combo.currentText()
        
        # 重新填充分类下拉框
        self.category_combo.clear()
        self.category_combo.addItem("全部")
        categories = self.phrase_library.get_categories()
        for category in categories.keys():
            self.category_combo.addItem(category)
        
        # 尝试恢复之前的分类选择
        index = self.category_combo.findText(current_category)
        if index >= 0:
            self.category_combo.setCurrentIndex(index)
        
        # 刷新列表
        self.filter_phrases()
    
    def manual_input_key_press(self, event):
        """处理手动输入框的键盘事件"""
        # Ctrl+Enter 快速确认
        if event.key() == Qt.Key_Return and event.modifiers() == Qt.ControlModifier:
            self.accept()
        else:
            # 调用原始的keyPressEvent
            QTextEdit.keyPressEvent(self.manual_input, event)
    
    def get_description(self) -> str:
        """获取用户输入的描述"""
        return self.manual_input.toPlainText().strip()
    
    def accept(self):
        """确认对话框，增强版本"""
        text = self.get_description()
        if not text:
            if self.quick_input_mode:
                # 快速模式下允许空输入
                super().accept()
                return
            else:
                QMessageBox.warning(self, "警告", "请输入描述或选择词库中的短语")
                return

        # 添加到历史记录
        self.add_to_recent_annotations(text)

        # 更新输入统计
        self.update_input_stats(text)

        # 更新自动完成数据
        self.setup_auto_completion()

        # 在快速模式下，提供额外的确认选项
        if self.quick_input_mode and len(text) < 3:
            reply = QMessageBox.question(
                self, "确认",
                f"输入较短: '{text}'，确定要使用吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

        super().accept()

    def get_input_confidence(self, text: str) -> float:
        """计算输入置信度"""
        if not text or not text.strip():
            return 0.0

        confidence = 0.0

        # 长度因子
        length_factor = min(1.0, len(text) / 20)  # 20字符为满分
        confidence += length_factor * 0.3

        # 词库匹配因子
        phrases = self.phrase_library.get_all_phrases()
        if text in phrases:
            confidence += 0.4
        elif any(phrase in text for phrase in phrases):
            confidence += 0.2

        # 历史匹配因子
        if text in self.recent_annotations:
            confidence += 0.3
        elif any(text in annotation for annotation in self.recent_annotations):
            confidence += 0.1

        return min(1.0, confidence)

    def suggest_improvements(self, text: str) -> List[str]:
        """建议改进"""
        suggestions = []

        if len(text) < 5:
            suggestions.append("建议增加更详细的描述")

        # 检查是否包含常用词
        common_words = [word for word, count in self.input_stats.get("common_words", [])]
        if not any(word in text.lower() for word in common_words[:5]):
            suggestions.append("考虑使用常用词汇以保持一致性")

        # 检查是否与词库匹配
        phrases = self.phrase_library.get_all_phrases()
        similar_phrases = [phrase for phrase in phrases if any(word in phrase.lower() for word in text.lower().split())]
        if similar_phrases:
            suggestions.append(f"相似的词库短语: {', '.join(similar_phrases[:3])}")

        return suggestions